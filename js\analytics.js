// Analytics Dashboard JavaScript
let analyticsData = [];
let sectorChart = null;
let monthlyChart = null;

// Color scheme matching the existing system
// Ordre: Premiers soins, Situations dangereuses, Presques accidents, Accidents de trajet, Accidents
const typeColors = {
    '5': { bg: 'rgba(59, 130, 246, 0.8)', border: 'rgb(59, 130, 246)', label: 'Premiers soins' },
    '3': { bg: 'rgba(34, 197, 94, 0.8)', border: 'rgb(34, 197, 94)', label: 'Situations dangereuses' },
    '2': { bg: 'rgba(255, 193, 7, 0.8)', border: 'rgb(255, 193, 7)', label: 'Presques accidents' },
    '4': { bg: 'rgba(251, 146, 60, 0.8)', border: 'rgb(251, 146, 60)', label: 'Accidents de trajet' },
    '1': { bg: 'rgba(239, 68, 68, 0.8)', border: 'rgb(239, 68, 68)', label: 'Accidents' }
};

// Function to get type (no more grouping)
function getGroupedType(typeAccident) {
    return typeAccident; // Retourner le type tel quel, plus de groupement
}

// Ordre défini pour les types dans les graphiques
const typeOrder = ['5', '3', '2', '4', '1'];

// Month names in French
const monthNames = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
];

$(document).ready(function() {
    // Initialize filters
    initializeAnalyticsFilters();

    // Load initial data
    loadAnalyticsData();

    // Event handlers
    $('#applyAnalyticsFilters').on('click', function() {
        applyAnalyticsFilters();
    });

    $('#resetAnalyticsFilters').on('click', function() {
        resetAnalyticsFilters();
    });

    $('#refreshChartsBtn').on('click', function() {
        loadAnalyticsData();
    });

    // Auto-apply filters when inputs change
    $('#analyticsStartDate, #analyticsEndDate, #analyticsSector, #analyticsType').on('change', function() {
        applyAnalyticsFilters();
    });
});

// Initialize filter dropdowns
function initializeAnalyticsFilters() {
    const currentYear = new Date().getFullYear();

    // Load sectors
    loadSelectData('ajax/get_secteurs.php', 'analyticsSector', s => s.nomSecteur);

    // Load accident types - populate manually since we know the types
    const typeSelect = $('#analyticsType');
    typeSelect.html(`
        <option value="">Tous les types</option>
        <option value="5">Premiers soins</option>
        <option value="3">Situations dangereuses</option>
        <option value="2">Presques accidents</option>
        <option value="4">Accidents de trajet</option>
        <option value="1">Accidents</option>
    `);

    // Set default date range (current year)
    $('#analyticsStartDate').val(`${currentYear}-01-01`);
    $('#analyticsEndDate').val(`${currentYear}-12-31`);
}

// Load analytics data from server
function loadAnalyticsData() {
    showLoading(true);

    $.ajax({
        url: 'ajax/get_accidents_data.php',
        dataType: 'json',
        timeout: 15000,
        success: function(data) {
            if (Array.isArray(data)) {
                analyticsData = data;
                applyAnalyticsFilters();
            } else {
                showError('Erreur lors du chargement des données');
            }
        },
        error: function(xhr, status, error) {
            showError('Erreur de connexion lors du chargement des données');
        },
        complete: function() {
            showLoading(false);
        }
    });
}

// Apply filters and update charts
function applyAnalyticsFilters() {
    if (analyticsData.length === 0) {
        return;
    }

    // Get filter values
    const startDate = $('#analyticsStartDate').val();
    const endDate = $('#analyticsEndDate').val();
    const sector = $('#analyticsSector').val();
    const type = $('#analyticsType').val();

    // Filter data
    let filteredData = analyticsData.filter(accident => {
        // Date filters
        if (startDate && accident.dateAccident < startDate) return false;
        if (endDate && accident.dateAccident > endDate) return false;

        // Sector filter - fix the logic to properly match sector names
        if (sector) {
            const sectorMatch =
                (accident.secteurAccident && accident.secteurAccident === sector) ||
                (accident.secteurEquipement && accident.secteurEquipement === sector);
            if (!sectorMatch) return false;
        }

        // Type filter
        if (type && accident.typeAccident != type) return false;

        return true;
    });

    // Update statistics
    updateStatistics(filteredData);

    // Update charts
    updateSectorChart(filteredData);
    updateMonthlyChart(filteredData);
}

// Update statistics cards
function updateStatistics(data) {
    const stats = {
        total: data.length,
        accidents: data.filter(a => a.typeAccident == '1').length, // Seulement les accidents (type 1)
        accidentsTrajet: data.filter(a => a.typeAccident == '4').length, // Accidents de trajet (type 4)
        premiersSoins: data.filter(a => a.typeAccident == '5').length,
        presqueAccidents: data.filter(a => a.typeAccident == '2').length,
        situationsDangereuses: data.filter(a => a.typeAccident == '3').length
    };

    $('#totalAccidents').text(stats.accidents); // Seulement les accidents (type 1)
    $('#totalAccidentsTrajet').text(stats.accidentsTrajet); // Accidents de trajet (type 4)
    $('#totalPremiersSoins').text(stats.premiersSoins);
    $('#totalPresqueAccidents').text(stats.presqueAccidents);
    $('#totalSituationsDangereuses').text(stats.situationsDangereuses);
}

// Update sector chart (stacked bar chart)
function updateSectorChart(data) {
    // Group data by sector and grouped type
    const sectorData = {};

    data.forEach(accident => {
        // Use sector name from the correct fields
        const sector = accident.secteurAccident || accident.secteurEquipement || 'Non défini';
        const groupedType = getGroupedType(accident.typeAccident);

        if (!sectorData[sector]) {
            sectorData[sector] = {};
        }

        if (!sectorData[sector][groupedType]) {
            sectorData[sector][groupedType] = 0;
        }

        sectorData[sector][groupedType]++;
    });

    // Prepare chart data
    const sectors = Object.keys(sectorData).sort();
    const types = typeOrder; // Utiliser l'ordre défini

    const datasets = types.map(type => ({
        label: typeColors[type].label,
        data: sectors.map(sector => sectorData[sector][type] || 0),
        backgroundColor: typeColors[type].bg,
        borderColor: typeColors[type].border,
        borderWidth: 1
    }));

    // Destroy existing chart
    if (sectorChart) {
        sectorChart.destroy();
    }

    // Create new chart
    const ctx = document.getElementById('sectorChart').getContext('2d');
    sectorChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: sectors,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Secteurs',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0
                    }
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Nombre d\'accidents',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            return `Secteur: ${context[0].label}`;
                        },
                        label: function(context) {
                            return `${context.dataset.label}: ${context.parsed.y}`;
                        },
                        footer: function(tooltipItems) {
                            const total = tooltipItems.reduce((sum, item) => sum + item.parsed.y, 0);
                            return `Total: ${total}`;
                        }
                    }
                }
            }
        }
    });
}

// Update monthly chart (stacked bar chart with multiple years)
function updateMonthlyChart(data) {
    // Get all unique years from the data
    const years = [...new Set(data.map(accident => new Date(accident.dateAccident).getFullYear()))].sort();

    // Group data by year, month and grouped type
    const monthlyData = {};

    // Initialize data structure for all years and months
    years.forEach(year => {
        monthlyData[year] = {};
        for (let i = 0; i < 12; i++) {
            monthlyData[year][i] = {};
            typeOrder.forEach(type => {
                monthlyData[year][i][type] = 0;
            });
        }
    });

    data.forEach(accident => {
        const date = new Date(accident.dateAccident);
        const year = date.getFullYear();
        const month = date.getMonth();
        const groupedType = getGroupedType(accident.typeAccident);

        if (monthlyData[year] && monthlyData[year][month] && typeColors[groupedType]) {
            monthlyData[year][month][groupedType]++;
        }
    });

    // Prepare chart data - create datasets for each type and year combination
    const types = typeOrder; // Utiliser l'ordre défini
    const datasets = [];

    years.forEach((year, yearIndex) => {
        types.forEach(type => {
            // Create more distinct colors for each year
            const baseColor = typeColors[type];
            let backgroundColor, borderColor;

            if (yearIndex === 0) {
                // Most recent year - full color
                backgroundColor = baseColor.bg;
                borderColor = baseColor.border;
            } else {
                // Older years - more muted colors
                backgroundColor = baseColor.bg.replace('0.8)', '0.4)');
                borderColor = baseColor.border.replace('1)', '0.6)');
            }

            datasets.push({
                label: `${baseColor.label}`,
                data: monthNames.map((_, monthIndex) => monthlyData[year][monthIndex][type] || 0),
                backgroundColor: backgroundColor,
                borderColor: borderColor,
                borderWidth: 1,
                stack: `${year}`, // Stack name is just the year
                categoryPercentage: 0.8,
                barPercentage: 0.9,
                year: year // Store year for reference
            });
        });
    });

    // Destroy existing chart
    if (monthlyChart) {
        monthlyChart.destroy();
    }

    // Create labels with years for better identification
    const labelsWithYears = monthNames.map(month => {
        if (years.length > 1) {
            return {
                month: month,
                years: years
            };
        }
        return month;
    });

    // Create new chart
    const ctx = document.getElementById('monthlyChart').getContext('2d');
    monthlyChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: monthNames,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: years.length > 1 ? `Mois (${years.join(' vs ')})` : 'Mois',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        callback: function(value, index) {
                            const month = monthNames[index];
                            if (years.length > 1) {
                                // Add year indicators below month names
                                return [month, years.join('  ')];
                            }
                            return month;
                        },
                        font: {
                            size: 11
                        }
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Nombre d\'évènements',
                        font: {
                            size: 14,
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: 12
                        },
                        // Show only unique type labels (remove duplicates)
                        generateLabels: function(chart) {
                            const uniqueLabels = new Map();
                            chart.data.datasets.forEach((dataset, index) => {
                                if (!uniqueLabels.has(dataset.label)) {
                                    uniqueLabels.set(dataset.label, {
                                        text: dataset.label,
                                        fillStyle: dataset.backgroundColor,
                                        strokeStyle: dataset.borderColor,
                                        lineWidth: dataset.borderWidth,
                                        hidden: false,
                                        index: index
                                    });
                                }
                            });
                            return Array.from(uniqueLabels.values());
                        }
                    }
                },
                tooltip: {
                    mode: 'nearest',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: 'white',
                    bodyColor: 'white',
                    borderColor: 'rgba(255, 255, 255, 0.2)',
                    borderWidth: 1,
                    callbacks: {
                        title: function(context) {
                            const year = context[0].dataset.year;
                            return `${context[0].label} ${year}`;
                        },
                        label: function(context) {
                            if (context.parsed.y === 0) return null;
                            return `${context.dataset.label}: ${context.parsed.y}`;
                        }
                    }
                }
            }
        }
    });
}

// Reset filters
function resetAnalyticsFilters() {
    const currentYear = new Date().getFullYear();
    $('#analyticsStartDate').val(`${currentYear}-01-01`);
    $('#analyticsEndDate').val(`${currentYear}-12-31`);
    $('#analyticsSector').val('');
    $('#analyticsType').val('');

    applyAnalyticsFilters();
}

// Function to redirect to accidents table with filters
function redirectToAccidents(category) {
    // Get current filter values
    const startDate = $('#analyticsStartDate').val();
    const endDate = $('#analyticsEndDate').val();
    const sectorId = $('#analyticsSector').val();

    // Convert sector ID to sector name for URL
    let sectorName = '';
    if (sectorId) {
        const sectorSelect = document.getElementById('analyticsSector');
        const selectedOption = sectorSelect.querySelector(`option[value="${sectorId}"]`);
        if (selectedOption) {
            sectorName = selectedOption.textContent;
        }
    }

    // Map category to accident type IDs
    let typeFilter = '';
    switch(category) {
        case 'accidents':
            typeFilter = '1'; // Seulement les accidents (type 1)
            break;
        case 'accidents-trajet':
            typeFilter = '4'; // Seulement les accidents de trajet (type 4)
            break;
        case 'premiers-soins':
            typeFilter = '5'; // Premier soins
            break;
        case 'presque-accidents':
            typeFilter = '2'; // Presque accident
            break;
        case 'situations-dangereuses':
            typeFilter = '3'; // Situation dangereuse
            break;
    }

    // Build URL with parameters
    let url = 'index.php?';
    const params = [];

    if (startDate) params.push(`startDate=${encodeURIComponent(startDate)}`);
    if (endDate) params.push(`endDate=${encodeURIComponent(endDate)}`);
    if (sectorName) params.push(`sector=${encodeURIComponent(sectorName)}`);
    if (typeFilter) params.push(`type=${encodeURIComponent(typeFilter)}`);

    url += params.join('&');

    // Redirect to accidents page
    window.location.href = url;
}

// Utility functions
function showLoading(show) {
    if (show) {
        $('#analyticsLoading').removeClass('hidden');
    } else {
        $('#analyticsLoading').addClass('hidden');
    }
}

function showError(message) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: message
        });
    } else {
        alert(message);
    }
}

// Load select data (reuse from main.js)
function loadSelectData(url, selectId, labelBuilder) {
    const select = document.getElementById(selectId);
    if (!select) {
        return;
    }

    const $select = $('#' + selectId);
    $select.html('<option value="">Chargement...</option>');

    $.ajax({
        url: url,
        dataType: 'json',
        timeout: 5000,
        success: function(data) {
            try {
                if (!Array.isArray(data)) {
                    throw new Error('Les données ne sont pas un tableau');
                }

                // Set appropriate default option based on selectId
                let defaultOption = '';
                if (selectId === 'analyticsSector') {
                    defaultOption = '<option value="">Tous les secteurs</option>';
                } else if (selectId === 'analyticsType') {
                    defaultOption = '<option value="">Tous les types</option>';
                } else {
                    defaultOption = '<option value="">Sélectionner...</option>';
                }

                $select.html(defaultOption);

                $.each(data, function(_, item) {
                    if (item && item.id !== undefined) {
                        // Use the sector name as value for proper filtering
                        const value = selectId === 'analyticsSector' ? labelBuilder(item) : item.id;
                        const option = $('<option>').val(value).text(labelBuilder(item));
                        $select.append(option);
                    }
                });
            } catch (error) {
                $select.html('<option value="">Erreur de chargement</option>');
            }
        },
        error: function(xhr, textStatus, errorThrown) {
            let defaultOption = '';
            if (selectId === 'analyticsSector') {
                defaultOption = '<option value="">Tous les secteurs</option>';
            } else if (selectId === 'analyticsType') {
                defaultOption = '<option value="">Tous les types</option>';
            } else {
                defaultOption = '<option value="">Erreur de chargement</option>';
            }
            $select.html(defaultOption);
        }
    });
}
