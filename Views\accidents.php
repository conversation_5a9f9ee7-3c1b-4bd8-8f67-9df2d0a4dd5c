<?php if (empty($accidents)): ?>
<div class="bg-white rounded-lg shadow-sm p-8 text-center border border-gray-200">
  <div class="flex flex-col items-center justify-center">
    <i class="fas fa-clipboard-list text-gray-300 text-5xl mb-4"></i>
    <h3 class="text-lg font-medium text-gray-900 mb-1">Aucun accident trouvé</h3>
    <p class="text-gray-500">Aucun accident ne correspond à vos critères de recherche.</p>
  </div>
</div>
<?php else: ?>

<div class="space-y-4" id="accident-table-container">
  <?php foreach ($accidents as $a): ?>
    <?php
    // Déterminer le nombre de personnes
    $nombrePersonnes = (int)($a['nombrePersonnes'] ?? 0);

    // Générer le HTML pour les personnes
    $personneHTML = '';
    if ($nombrePersonnes === 0) {
        $personneHTML = '<span class="text-gray-500 italic">Aucune personne</span>';
    } elseif ($nombrePersonnes === 1) {
        if (!empty($a['prenomPerso']) && !empty($a['nomPerso'])) {
            $personneHTML = htmlspecialchars($a['prenomPerso'] . ' ' . $a['nomPerso']);
        } elseif (!empty($a['personneLibre'])) {
            $personneHTML = htmlspecialchars($a['personneLibre']);
        } else {
            $personneHTML = '<span class="text-gray-500 italic">Non spécifiée</span>';
        }
    } else {
        if (!empty($a['prenomPerso']) && !empty($a['nomPerso'])) {
            $personneHTML = htmlspecialchars($a['prenomPerso'] . ' ' . $a['nomPerso']);
        } elseif (!empty($a['personneLibre'])) {
            $personneHTML = htmlspecialchars($a['personneLibre']);
        } else {
            $personneHTML = '<span class="text-gray-500 italic">Personne 1</span>';
        }
        $personneHTML .= ' <button class="ml-2 inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full hover:bg-blue-200 transition-colors show-all-personnes-btn" data-accident-id="' . $a['id'] . '" title="Voir toutes les personnes impliquées">+' . ($nombrePersonnes - 1) . '</button>';
    }

    // Déterminer le type et sa couleur
    $typeClass = '';
    $typeLabel = '';
    $typeIcon = '';

    switch ((int)$a['typeAccident']) {
        case 1: // Accident
            $typeClass = 'bg-red-100 text-red-800 border-red-200';
            $typeLabel = 'Accident';
            $typeIcon = 'fas fa-exclamation-triangle';
            break;
        case 4: // Accident de trajet
            $typeClass = 'bg-orange-100 text-orange-800 border-orange-200';
            $typeLabel = 'Accident de trajet';
            $typeIcon = 'fas fa-car-crash';
            break;
        case 5: // Premier soins
            $typeClass = 'bg-blue-100 text-blue-800 border-blue-200';
            $typeLabel = 'Premier soins';
            $typeIcon = 'fas fa-first-aid';
            break;
        case 2: // Presque accident
            $typeClass = 'bg-yellow-100 text-yellow-800 border-yellow-200';
            $typeLabel = 'Presque accident';
            $typeIcon = 'fas fa-exclamation';
            break;
        case 3: // Situation dangereuse
            $typeClass = 'bg-green-100 text-green-800 border-green-200';
            $typeLabel = 'Situation dangereuse';
            $typeIcon = 'fas fa-shield-alt';
            break;
        default:
            $typeClass = 'bg-gray-100 text-gray-800 border-gray-200';
            $typeLabel = 'Non défini';
            $typeIcon = 'fas fa-question';
    }

    // Fonction pour afficher les secteurs
    $secteurHTML = '';
    if (!empty($a['secteurEquipement'])) {
        $secteurHTML .= '<span class="text-xs text-gray-500">Secteur équipement: ' . htmlspecialchars($a['secteurEquipement']) . '</span>';
    }
    if (!empty($a['secteurAccident']) && $a['secteurAccident'] != $a['secteurEquipement']) {
        if ($secteurHTML) $secteurHTML .= '<br>';
        $secteurHTML .= '<span class="text-xs text-blue-600">Secteur accident: ' . htmlspecialchars($a['secteurAccident']) . '</span>';
    }
    ?>

    <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow" data-accident-id="<?= $a['id'] ?>">
      <!-- En-tête de la carte -->
      <div class="flex items-start justify-between p-4 border-b border-gray-100">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="w-10 h-10 rounded-full <?= $typeClass ?> flex items-center justify-center">
              <i class="<?= $typeIcon ?> text-sm"></i>
            </div>
          </div>
          <div>
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-900">
                <?= htmlspecialchars(date('d/m/Y', strtotime($a['dateAccident']))) ?>
              </span>
              <span class="px-2 py-1 text-xs font-medium rounded-full border <?= $typeClass ?>">
                <?= htmlspecialchars($typeLabel) ?>
              </span>
            </div>
            <div class="text-sm text-gray-600 mt-1">
              <?= $personneHTML ?>
            </div>
          </div>
        </div>

        <!-- Menu actions -->
        <div class="relative">
          <button class="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors" onclick="toggleCardMenu(<?= $a['id'] ?>)">
            <i class="fas fa-ellipsis-v"></i>
          </button>
          <div id="card-menu-<?= $a['id'] ?>" class="hidden absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
            <div class="py-1">
              <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 edit-btn" data-id="<?= $a['id'] ?>" data-bs-toggle="modal" data-bs-target="#editAccidentModal">
                <i class="fas fa-edit mr-2"></i> Modifier
              </button>
              <button class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 delete-btn" data-id="<?= $a['id'] ?>">
                <i class="fas fa-trash mr-2"></i> Supprimer
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Corps de la carte -->
      <div class="p-4">
        <!-- Informations principales sur une ligne -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <!-- Équipement -->
          <div>
            <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Équipement</label>
            <div class="text-sm text-gray-900 mt-1">
              <?= htmlspecialchars($a['nomEquipement'] ?? '-') ?>
              <?php if ($secteurHTML): ?>
                <br><?= $secteurHTML ?>
              <?php endif; ?>
            </div>
          </div>

          <!-- Partie blessée -->
          <div>
            <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Partie blessée</label>
            <div class="text-sm <?= !empty($a['partieBlessée']) ? 'text-red-600 font-medium' : 'text-gray-900' ?> mt-1">
              <?= htmlspecialchars($a['partieBlessée'] ?? '-') ?>
            </div>
          </div>

          <!-- Contrat -->
          <div>
            <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Contrat</label>
            <div class="text-sm text-gray-900 mt-1"><?= htmlspecialchars($a['typeContrat'] ?? '-') ?></div>
          </div>
        </div>

        <!-- Jours d'arrêt si présent -->
        <?php if (!empty($a['joursArrets']) && $a['joursArrets'] > 0): ?>
        <div class="mb-4">
          <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Jours d'arrêt</label>
          <div class="text-sm text-gray-900 font-medium mt-1"><?= htmlspecialchars($a['joursArrets']) ?> jour<?= $a['joursArrets'] > 1 ? 's' : '' ?></div>
        </div>
        <?php endif; ?>

        <!-- Cause et remarques sur la même ligne -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Cause -->
          <div>
            <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Cause</label>
            <div class="text-sm text-gray-900 mt-1 break-words"><?= htmlspecialchars($a['causeAccident'] ?? '-') ?></div>
          </div>

          <!-- Remarques -->
          <div>
            <label class="text-xs font-medium text-gray-500 uppercase tracking-wide">Remarques</label>
            <div class="text-sm text-gray-600 mt-1 break-words"><?= htmlspecialchars($a['remarquesAccident'] ?? '-') ?></div>
          </div>
        </div>
      </div>
    </div>
  <?php endforeach; ?>
</div>

<!-- Modal pour afficher toutes les personnes d'un accident -->
<div id="personnesModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
  <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
    <div class="mt-3">
      <!-- Header -->
      <div class="flex justify-between items-center pb-3 border-b">
        <h3 class="text-lg font-medium text-gray-900">
          <i class="fas fa-users mr-2 text-blue-500"></i>
          Personnes impliquées dans l'évènement
        </h3>
        <button type="button" class="text-gray-400 hover:text-gray-600 focus:outline-none" onclick="closePersonnesModal()">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Contenu -->
      <div class="mt-4">
        <div id="personnesModalContent" class="space-y-4">
          <!-- Le contenu sera chargé dynamiquement -->
        </div>
      </div>

      <!-- Footer -->
      <div class="flex justify-end pt-4 border-t mt-4">
        <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="closePersonnesModal()">
          Fermer
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  // Update accident count
  document.getElementById('accident-count').textContent = '<?= count($accidents) ?>';

  // Fonction pour gérer le menu des cartes
  window.toggleCardMenu = function(accidentId) {
    const menu = document.getElementById(`card-menu-${accidentId}`);
    const allMenus = document.querySelectorAll('[id^="card-menu-"]');

    // Fermer tous les autres menus
    allMenus.forEach(m => {
        if (m.id !== `card-menu-${accidentId}`) {
            m.classList.add('hidden');
        }
    });

    // Basculer le menu actuel
    menu.classList.toggle('hidden');
  };

  // Fermer les menus quand on clique ailleurs
  document.addEventListener('click', function(e) {
    if (!e.target.closest('[onclick*="toggleCardMenu"]') && !e.target.closest('[id^="card-menu-"]')) {
        const allMenus = document.querySelectorAll('[id^="card-menu-"]');
        allMenus.forEach(menu => menu.classList.add('hidden'));
    }
  });

  // Fonction pour ouvrir le modal des personnes
  function openPersonnesModal(accidentId) {
    const modal = document.getElementById('personnesModal');
    const content = document.getElementById('personnesModalContent');

    // Afficher le modal
    modal.classList.remove('hidden');

    // Afficher un loader
    content.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i><p class="text-gray-500 mt-2">Chargement...</p></div>';

    // Charger les données
    fetch(`Ajax/get_accident_personnes.php?idAccident=${accidentId}`)
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          content.innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Erreur lors du chargement</p></div>';
          return;
        }

        if (data.length === 0) {
          content.innerHTML = '<div class="text-center py-4 text-gray-500"><i class="fas fa-user-slash text-2xl"></i><p class="mt-2">Aucune personne impliquée</p></div>';
          return;
        }

        // Générer le HTML pour chaque personne
        let html = '';
        data.forEach((personne, index) => {
          html += generatePersonneCard(personne, index + 1);
        });

        content.innerHTML = html;
      })
      .catch(error => {
        console.error('Erreur:', error);
        content.innerHTML = '<div class="text-center py-4 text-red-500"><i class="fas fa-exclamation-triangle text-2xl"></i><p class="mt-2">Erreur lors du chargement</p></div>';
      });
  }

  // Fonction pour fermer le modal des personnes
  function closePersonnesModal() {
    const modal = document.getElementById('personnesModal');
    modal.classList.add('hidden');
  }

  // Fonction pour générer le HTML d'une carte personne
  function generatePersonneCard(personne, numero) {
    const nomComplet = personne.prenomPerso && personne.nomPerso
      ? `${personne.prenomPerso} ${personne.nomPerso}`
      : personne.personneLibre || '';

    const typePersonne = personne.prenomPerso && personne.nomPerso ? 'Salarié SCM' : 'Personne externe';

    return `
      <div class="bg-gray-50 p-4 rounded-lg border">
        <div class="flex items-start justify-between mb-3">
          <h4 class="text-md font-medium text-gray-900">
            <i class="fas fa-user mr-2 text-blue-500"></i>
            Personne ${numero}
          </h4>
          <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
            ${typePersonne}
          </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-700">Nom</p>
            <p class="text-sm text-gray-900">${nomComplet}</p>
            ${personne.secteurPersonne ? `<p class="text-xs text-gray-500">Secteur: ${personne.secteurPersonne}</p>` : ''}
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Type de contrat</p>
            <p class="text-sm text-gray-900">${personne.typeContrat || '-'}</p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Partie blessée</p>
            <p class="text-sm text-gray-900">${personne.partieBlessée || '-'}</p>
          </div>

          <div>
            <p class="text-sm font-medium text-gray-700">Jours d'arrêt</p>
            <p class="text-sm text-gray-900">
              ${personne.joursArrets && personne.joursArrets > 0
                ? `${personne.joursArrets} jour${personne.joursArrets > 1 ? 's' : ''}`
                : '0 jour'}
            </p>
          </div>
        </div>
      </div>
    `;
  }

  // Gestionnaire d'événements pour les boutons "+X"
  document.addEventListener('DOMContentLoaded', function() {
    document.addEventListener('click', function(e) {
      if (e.target.classList.contains('show-all-personnes-btn') || e.target.closest('.show-all-personnes-btn')) {
        const btn = e.target.classList.contains('show-all-personnes-btn') ? e.target : e.target.closest('.show-all-personnes-btn');
        const accidentId = btn.getAttribute('data-accident-id');
        openPersonnesModal(accidentId);
      }
    });

    // Fermer le modal en cliquant à l'extérieur
    document.getElementById('personnesModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closePersonnesModal();
      }
    });
  });
</script>
<?php endif; ?>
